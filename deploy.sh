#!/bin/bash

echo "🚀 بدء النشر التلقائي..."

# 1. Initialize git if needed
if [ ! -d ".git" ]; then
    git init
    echo "✅ تم إعداد Git"
fi

# 2. Add all files
git add .
git commit -m "Deploy: أكاديمية النجاح $(date)"
echo "✅ تم حفظ التغييرات"

# 3. Deploy instructions
echo ""
echo "🌐 روابط النشر السريع:"
echo ""
echo "1️⃣ Railway (الأسهل):"
echo "   - اذهب إلى: https://railway.app"
echo "   - اضغط 'New Project'"
echo "   - اختر 'Deploy from GitHub repo'"
echo "   - اختر هذا المستودع"
echo ""
echo "2️⃣ Vercel:"
echo "   - اذهب إلى: https://vercel.com"
echo "   - اضغط 'New Project'"
echo "   - اختر هذا المستودع"
echo ""
echo "3️⃣ Render:"
echo "   - اذهب إلى: https://render.com"
echo "   - اضغط 'New Web Service'"
echo "   - اختر هذا المستودع"
echo ""
echo "🔑 متغيرات البيئة المطلوبة:"
echo "MONGODB_URI=mongodb+srv://academy:<EMAIL>/najah-academy?retryWrites=true&w=majority"
echo "JWT_SECRET=najah-academy-super-secret-2024"
echo "NODE_ENV=production"
echo ""
echo "✅ النشر جاهز! اتبع الروابط أعلاه"
