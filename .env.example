# Environment Configuration
NODE_ENV=production
PORT=3000

# Database Configuration (Required)
MONGODB_URI=mongodb+srv://username:<EMAIL>/najah-academy?retryWrites=true&w=majority

# JWT Configuration (Required)
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# Admin Configuration (Required)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Admin123!@#

# Site Configuration
SITE_NAME=أكاديمية النجاح
SITE_URL=https://your-domain.com

# Email Configuration (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password

# Upload Configuration (Optional)
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=jpg,jpeg,png,pdf,doc,docx

# Security Configuration (Optional)
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
SESSION_SECRET=your-session-secret
