# 🌐 نشر سريع على Netlify

## الطريقة الأولى: السحب والإفلات (الأسرع)

### الخطوات:
1. **توقف عن الخادم المحلي:**
   - اضغط `Ctrl + C` في Terminal

2. **اذهب إلى Netlify:**
   - افتح [netlify.com](https://netlify.com)
   - انقر "Sign up" أو "Log in"
   - سجل دخول بـ GitHub أو Google

3. **نشر المشروع:**
   - في الصفحة الرئيسية، ستجد منطقة "Want to deploy a new site without connecting to Git?"
   - اسحب مجلد المشروع كاملاً إلى هذه المنطقة
   - أو انقر "Browse to upload" واختر المجلد

4. **انتظار النشر:**
   - سيبدأ Netlify في رفع الملفات
   - انتظر 2-3 دقائق
   - ستحصل على رابط مثل: `https://amazing-name-123456.netlify.app`

## الطريقة الثانية: Netlify CLI

### التثبيت:
```bash
npm install -g netlify-cli
```

### النشر:
```bash
netlify login
netlify deploy
netlify deploy --prod
```

## ⚠️ ملاحظات مهمة:

### قاعدة البيانات:
- Netlify يدعم المواقع الثابتة فقط
- التطبيق سيعمل بقاعدة البيانات JSON المحلية
- لن تتمكن من إضافة بيانات جديدة عبر الإنترنت

### الملفات المطلوبة:
- تأكد من وجود جميع ملفات HTML, CSS, JS
- تأكد من وجود مجلد `data` مع ملفات JSON
- تأكد من وجود مجلد `public` مع الصور

### إعدادات إضافية:
إذا كنت تريد استخدام النماذج (Contact Form):
1. اذهب إلى Site Settings في Netlify
2. اذهب إلى Forms
3. فعل Form detection

## 🎯 النتيجة المتوقعة:
- موقع يعمل بالكامل
- جميع الصفحات تعمل
- التصميم يظهر بشكل صحيح
- النماذج تعمل (مع إعداد إضافي)
- سرعة عالية جداً

## 🔗 الرابط:
بعد النشر ستحصل على رابط مثل:
`https://najah-academy-123456.netlify.app`

يمكنك تغيير اسم الموقع من إعدادات Netlify.

## 📱 اختبار الموقع:
1. افتح الرابط في المتصفح
2. تأكد من عمل جميع الصفحات
3. اختبر التصميم على الهاتف
4. اختبر النماذج

## 🚀 نشر سريع الآن:
1. توقف عن الخادم: `Ctrl + C`
2. اذهب إلى: https://netlify.com
3. اسحب مجلد المشروع
4. انتظر النشر
5. احصل على الرابط!
