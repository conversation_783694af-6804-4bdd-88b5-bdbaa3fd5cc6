name: Deploy to Railway

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test --if-present
      
    - name: Build application
      run: npm run build --if-present
      
    - name: Deploy to Railway
      if: github.ref == 'refs/heads/main'
      run: |
        echo "🚀 Ready for deployment!"
        echo "Visit Railway.app to complete deployment"
