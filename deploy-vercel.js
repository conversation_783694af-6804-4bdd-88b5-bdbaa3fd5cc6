#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 نشر التطبيق على Vercel...\n');

// التحقق من تثبيت Vercel CLI
console.log('🛠️ التحقق من Vercel CLI...');
try {
    execSync('vercel --version', { stdio: 'pipe' });
    console.log('✅ Vercel CLI مثبت');
} catch (error) {
    console.log('❌ Vercel CLI غير مثبت');
    console.log('📥 تثبيت Vercel CLI...');
    try {
        execSync('npm install -g vercel', { stdio: 'inherit' });
        console.log('✅ تم تثبيت Vercel CLI');
    } catch (installError) {
        console.log('❌ فشل في تثبيت Vercel CLI');
        console.log('يرجى تثبيته يدوياً: npm install -g vercel');
        process.exit(1);
    }
}

// إنشاء ملف vercel.json محدث
console.log('\n📝 إنشاء ملف vercel.json...');
const vercelConfig = {
    "version": 2,
    "builds": [
        {
            "src": "server-mongo.js",
            "use": "@vercel/node"
        }
    ],
    "routes": [
        {
            "src": "/api/(.*)",
            "dest": "/server-mongo.js"
        },
        {
            "src": "/(.*)",
            "dest": "/server-mongo.js"
        }
    ],
    "env": {
        "NODE_ENV": "production"
    },
    "functions": {
        "server-mongo.js": {
            "maxDuration": 30
        }
    }
};

fs.writeFileSync('vercel.json', JSON.stringify(vercelConfig, null, 2));
console.log('✅ تم إنشاء ملف vercel.json');

// تسجيل الدخول إلى Vercel
console.log('\n🔐 تسجيل الدخول إلى Vercel...');
try {
    execSync('vercel login', { stdio: 'inherit' });
    console.log('✅ تم تسجيل الدخول بنجاح');
} catch (error) {
    console.log('❌ فشل في تسجيل الدخول');
    process.exit(1);
}

// نشر التطبيق
console.log('\n🚀 نشر التطبيق...');
try {
    // نشر مع إعداد متغيرات البيئة
    const deployCommand = 'vercel --prod';
    
    // قراءة متغيرات البيئة من ملف .env
    if (fs.existsSync('.env')) {
        console.log('📋 قراءة متغيرات البيئة...');
        const envContent = fs.readFileSync('.env', 'utf8');
        const envLines = envContent.split('\n').filter(line => 
            line.trim() && !line.startsWith('#')
        );
        
        console.log('🔧 إعداد متغيرات البيئة في Vercel...');
        console.log('يرجى إضافة المتغيرات التالية في لوحة تحكم Vercel:');
        console.log('https://vercel.com/dashboard -> Project Settings -> Environment Variables\n');
        
        envLines.forEach(line => {
            const [key, ...valueParts] = line.split('=');
            const value = valueParts.join('=');
            if (key && value) {
                console.log(`${key}=${value}`);
            }
        });
        
        console.log('\nاضغط Enter بعد إضافة المتغيرات...');
        require('child_process').execSync('pause', { stdio: 'inherit' });
    }
    
    execSync(deployCommand, { stdio: 'inherit' });
    console.log('\n✅ تم نشر التطبيق بنجاح!');
    
} catch (error) {
    console.log('❌ فشل في نشر التطبيق');
    console.log('يرجى المحاولة مرة أخرى أو النشر يدوياً');
    process.exit(1);
}

console.log('\n🎉 تم النشر بنجاح على Vercel!');
console.log('🌐 يمكنك الوصول لتطبيقك من الرابط الذي ظهر أعلاه');
console.log('📊 لوحة الإدارة: [your-domain]/admin');
console.log('🎓 لوحة الطالب: [your-domain]/student');
