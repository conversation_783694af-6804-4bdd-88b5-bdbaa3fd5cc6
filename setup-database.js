#!/usr/bin/env node

require('dotenv').config();
const mongoose = require('mongoose');

// Import models
const User = require('./models/User');
const Course = require('./models/Course');
const Category = require('./models/Category');

console.log('🗄️ إعداد قاعدة البيانات...\n');

async function setupDatabase() {
    try {
        // الاتصال بقاعدة البيانات
        console.log('🔌 الاتصال بـ MongoDB Atlas...');
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ تم الاتصال بنجاح');

        // إنشاء المستخدم الإداري
        console.log('\n👤 إنشاء المستخدم الإداري...');
        const adminExists = await User.findOne({ email: process.env.ADMIN_EMAIL });
        if (!adminExists) {
            const admin = new User({
                name: 'مدير النظام',
                email: process.env.ADMIN_EMAIL || '<EMAIL>',
                password: process.env.ADMIN_PASSWORD || 'Admin123!@#',
                role: 'admin',
                emailVerified: true
            });
            await admin.save();
            console.log('✅ تم إنشاء المستخدم الإداري');
        } else {
            console.log('✅ المستخدم الإداري موجود بالفعل');
        }

        // إنشاء الفئات الافتراضية
        console.log('\n📂 إنشاء الفئات الافتراضية...');
        const categoryCount = await Category.countDocuments();
        if (categoryCount === 0) {
            const defaultCategories = [
                {
                    name: 'التسويق',
                    description: 'دورات التسويق والإعلان الرقمي',
                    icon: 'fas fa-bullhorn',
                    color: '#667eea'
                },
                {
                    name: 'المبيعات',
                    description: 'دورات المبيعات وخدمة العملاء',
                    icon: 'fas fa-chart-line',
                    color: '#28a745'
                },
                {
                    name: 'إدارة الوقت',
                    description: 'دورات إدارة الوقت والإنتاجية',
                    icon: 'fas fa-clock',
                    color: '#ffc107'
                },
                {
                    name: 'ريادة الأعمال',
                    description: 'دورات ريادة الأعمال والمشاريع',
                    icon: 'fas fa-rocket',
                    color: '#17a2b8'
                }
            ];

            for (const catData of defaultCategories) {
                const category = new Category(catData);
                await category.save();
            }
            console.log('✅ تم إنشاء الفئات الافتراضية');
        } else {
            console.log('✅ الفئات موجودة بالفعل');
        }

        // إنشاء كورس تجريبي
        console.log('\n📚 إنشاء الكورس التجريبي...');
        const courseCount = await Course.countDocuments();
        if (courseCount === 0) {
            const marketingCategory = await Category.findOne({ name: 'التسويق' });
            const admin = await User.findOne({ role: 'admin' });
            
            const sampleCourse = new Course({
                title: 'فن التسويق والمبيعات وإدارة الوقت',
                description: 'كورس شامل في التسويق والمبيعات وإدارة الوقت للمشاريع الصغيرة ورواد الأعمال',
                shortDescription: 'تعلم أساسيات التسويق والمبيعات وإدارة الوقت بطريقة عملية ومبسطة',
                category: marketingCategory._id,
                level: 'مبتدئ',
                price: {
                    regular: 299,
                    currency: 'EGP'
                },
                duration: {
                    total: '8 ساعات',
                    weeks: 6
                },
                instructors: [admin._id],
                status: 'active',
                isPublished: true,
                publishedAt: new Date(),
                requirements: [
                    'لا توجد متطلبات مسبقة',
                    'الرغبة في التعلم والتطوير',
                    'إمكانية الوصول للإنترنت'
                ],
                learningOutcomes: [
                    'فهم أساسيات التسويق الحديث',
                    'إتقان مهارات البيع والإقناع',
                    'تطبيق تقنيات إدارة الوقت الفعالة',
                    'بناء خطة تسويقية شاملة'
                ],
                targetAudience: [
                    'أصحاب المشاريع الصغيرة',
                    'موظفي المبيعات والتسويق',
                    'رواد الأعمال الجدد',
                    'الطلاب والخريجين'
                ],
                modules: [
                    {
                        title: 'أساسيات التسويق',
                        description: 'مقدمة شاملة في مفاهيم التسويق الحديث',
                        order: 1,
                        lessons: [
                            {
                                title: 'ما هو التسويق ولماذا هو مهم؟',
                                description: 'فهم مفهوم التسويق وأهميته في نجاح الأعمال',
                                type: 'video',
                                duration: '15:30',
                                order: 1,
                                isPreview: true
                            },
                            {
                                title: 'الفرق بين التسويق والمبيعات',
                                description: 'التمييز بين التسويق والمبيعات ودور كل منهما',
                                type: 'video',
                                duration: '12:45',
                                order: 2
                            }
                        ]
                    }
                ]
            });
            
            await sampleCourse.save();
            console.log('✅ تم إنشاء الكورس التجريبي');
        } else {
            console.log('✅ الكورسات موجودة بالفعل');
        }

        console.log('\n🎉 تم إعداد قاعدة البيانات بنجاح!');
        
        // عرض معلومات الاتصال
        console.log('\n📊 معلومات قاعدة البيانات:');
        console.log(`- اسم قاعدة البيانات: ${mongoose.connection.name}`);
        console.log(`- عدد المستخدمين: ${await User.countDocuments()}`);
        console.log(`- عدد الفئات: ${await Category.countDocuments()}`);
        console.log(`- عدد الكورسات: ${await Course.countDocuments()}`);
        
        process.exit(0);
    } catch (error) {
        console.error('❌ خطأ في إعداد قاعدة البيانات:', error.message);
        process.exit(1);
    }
}

setupDatabase();
