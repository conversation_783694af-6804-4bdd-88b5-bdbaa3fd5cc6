#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

console.log('⚡ بدء التشغيل السريع...\n');

// Start with the MongoDB version that has fallback
const startCommand = 'node server-mongo.js';

console.log('🔧 تحقق من الإعدادات...');

// Check if .env exists and has MONGODB_URI
const envContent = fs.readFileSync('.env', 'utf8');
if (!envContent.includes('MONGODB_URI')) {
    console.log('⚠️  قاعدة البيانات غير مُعدة، سيتم التشغيل بوضع التجريب');
}

console.log('🚀 بدء تشغيل الخادم...\n');

try {
    execSync(startCommand, { stdio: 'inherit' });
} catch (error) {
    console.log('\n❌ فشل في الاتصال بقاعدة البيانات، التبديل للوضع المحلي...\n');
    
    // Fallback to JSON database
    try {
        execSync('node server.js', { stdio: 'inherit' });
    } catch (fallbackError) {
        console.error('❌ خطأ في التشغيل:', fallbackError.message);
    }
}
