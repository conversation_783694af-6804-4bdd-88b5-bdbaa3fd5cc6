#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 بدء عملية النشر السريع...\n');

// التحقق من وجود الملفات المطلوبة
const requiredFiles = [
    'server-mongo.js',
    'package.json',
    'railway.json',
    '.env'
];

console.log('📋 التحقق من الملفات المطلوبة...');
for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file} موجود`);
    } else {
        console.log(`❌ ${file} غير موجود`);
        process.exit(1);
    }
}

// التحقق من تثبيت Railway CLI
console.log('\n🛠️ التحقق من Railway CLI...');
try {
    execSync('railway --version', { stdio: 'pipe' });
    console.log('✅ Railway CLI مثبت');
} catch (error) {
    console.log('❌ Railway CLI غير مثبت');
    console.log('📥 تثبيت Railway CLI...');
    try {
        execSync('npm install -g @railway/cli', { stdio: 'inherit' });
        console.log('✅ تم تثبيت Railway CLI');
    } catch (installError) {
        console.log('❌ فشل في تثبيت Railway CLI');
        console.log('يرجى تثبيته يدوياً: npm install -g @railway/cli');
        process.exit(1);
    }
}

// تسجيل الدخول إلى Railway
console.log('\n🔐 تسجيل الدخول إلى Railway...');
try {
    execSync('railway login', { stdio: 'inherit' });
    console.log('✅ تم تسجيل الدخول بنجاح');
} catch (error) {
    console.log('❌ فشل في تسجيل الدخول');
    process.exit(1);
}

// إنشاء مشروع جديد أو ربط موجود
console.log('\n📦 إعداد المشروع...');
try {
    // محاولة ربط مشروع موجود أولاً
    try {
        execSync('railway status', { stdio: 'pipe' });
        console.log('✅ مشروع Railway موجود بالفعل');
    } catch {
        // إنشاء مشروع جديد
        console.log('🆕 إنشاء مشروع Railway جديد...');
        execSync('railway init', { stdio: 'inherit' });
        console.log('✅ تم إنشاء المشروع');
    }
} catch (error) {
    console.log('❌ فشل في إعداد المشروع');
    process.exit(1);
}

// رفع متغيرات البيئة
console.log('\n🔧 رفع متغيرات البيئة...');
try {
    if (fs.existsSync('.env')) {
        const envContent = fs.readFileSync('.env', 'utf8');
        const envLines = envContent.split('\n').filter(line => 
            line.trim() && !line.startsWith('#')
        );
        
        for (const line of envLines) {
            const [key, ...valueParts] = line.split('=');
            const value = valueParts.join('=');
            if (key && value) {
                try {
                    execSync(`railway variables set ${key}="${value}"`, { stdio: 'pipe' });
                    console.log(`✅ ${key} تم رفعه`);
                } catch (varError) {
                    console.log(`⚠️ تحذير: فشل في رفع ${key}`);
                }
            }
        }
    }
} catch (error) {
    console.log('⚠️ تحذير: فشل في رفع بعض متغيرات البيئة');
}

// نشر التطبيق
console.log('\n🚀 نشر التطبيق...');
try {
    execSync('railway up', { stdio: 'inherit' });
    console.log('\n✅ تم نشر التطبيق بنجاح!');
    
    // الحصول على رابط التطبيق
    try {
        const domain = execSync('railway domain', { encoding: 'utf8' }).trim();
        console.log(`\n🌐 رابط التطبيق: ${domain}`);
        console.log(`📊 لوحة الإدارة: ${domain}/admin`);
        console.log(`🎓 لوحة الطالب: ${domain}/student`);
    } catch {
        console.log('\n🌐 يمكنك الحصول على رابط التطبيق من لوحة تحكم Railway');
    }
    
} catch (error) {
    console.log('❌ فشل في نشر التطبيق');
    console.log('يرجى المحاولة مرة أخرى أو النشر يدوياً');
    process.exit(1);
}

console.log('\n🎉 تم النشر بنجاح! يمكنك الآن الوصول لتطبيقك على الإنترنت.');
