# 🚀 دليل نشر التطبيق على Railway

## الخطوات المطلوبة:

### 1. إنشاء حساب MongoDB Atlas
1. اذهب إلى [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. أنشئ حساب جديد أو سجل دخول
3. أنشئ مشروع جديد باسم "Najah Academy"
4. أنشئ Cluster جديد (اختر المجاني M0)
5. أنشئ مستخدم قاعدة بيانات:
   - Username: `najahacademy`
   - Password: `NajahAcademy2024`
6. أضف عنوان IP: `0.0.0.0/0` (للسماح بالوصول من أي مكان)
7. احصل على Connection String

### 2. نشر على Railway
1. اذهب إلى [Railway](https://railway.app)
2. سجل دخول باستخدام GitHub
3. أنشئ مشروع جديد
4. ارب<PERSON> مستودع GitHub أو ارفع الملفات
5. <PERSON><PERSON><PERSON> متغيرات البيئة:
   ```
   NODE_ENV=production
   MONGODB_URI=mongodb+srv://najahacademy:<EMAIL>/najah-academy?retryWrites=true&w=majority
   JWT_SECRET=najah-academy-super-secret-jwt-key-2024-production
   JWT_EXPIRES_IN=24h
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=Admin123!@#
   SITE_NAME=أكاديمية النجاح
   ```

### 3. إعداد النطاق
- Railway سيعطيك رابط مجاني مثل: `https://your-app.railway.app`
- يمكنك ربط نطاق مخصص لاحقاً

### 4. اختبار التطبيق
بعد النشر، اختبر:
- الصفحة الرئيسية: `/`
- تسجيل الدخول: `/login`
- لوحة الإدارة: `/admin`
- لوحة الطالب: `/student`

## الملفات المهمة للنشر:
- `server-mongo.js` - الخادم الرئيسي
- `package.json` - التبعيات
- `railway.json` - إعدادات Railway
- `.env` - متغيرات البيئة (لا ترفعها لـ GitHub)

## أوامر مفيدة:
```bash
# تشغيل محلي
npm run dev

# تشغيل الإنتاج
npm start

# نشر على Railway
railway up
```
