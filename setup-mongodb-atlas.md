# 🗄️ إعداد قاعدة بيانات MongoDB Atlas

## الخطوات:

### 1. إنشاء حساب MongoDB Atlas
1. اذهب إلى [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. أنشئ حساب جديد أو سجل دخول
3. أنشئ مشروع جديد باسم "Najah Academy"

### 2. إنشاء Cluster
1. اختر "Build a Database"
2. اختر "M0 Sandbox" (المجاني)
3. اختر المنطقة الأقرب (مثل Frankfurt أو London)
4. اسم الـ Cluster: `Cluster0`

### 3. إعداد الأمان
1. **Database User:**
   - Username: `najahacademy`
   - Password: `NajahAcademy2024!`
   - Database User Privileges: Read and write to any database

2. **Network Access:**
   - Add IP Address: `0.0.0.0/0` (Allow access from anywhere)
   - أو يمكنك إضافة عناوين IP محددة للأمان

### 4. الحصول على Connection String
1. اذهب إلى "Database" -> "Connect"
2. اختر "Connect your application"
3. اختر "Node.js" و "4.1 or later"
4. انسخ Connection String:
```
mongodb+srv://najahacademy:<password>@cluster0.xxxxx.mongodb.net/?retryWrites=true&w=majority
```

### 5. تحديث ملف .env
استبدل `<password>` بكلمة المرور الفعلية:
```
MONGODB_URI=mongodb+srv://najahacademy:NajahAcademy2024!@cluster0.xxxxx.mongodb.net/najah-academy?retryWrites=true&w=majority
```

### 6. اختبار الاتصال
```bash
node setup-database.js
```

## ملاحظات مهمة:
- تأكد من استبدال `xxxxx` برقم الـ cluster الفعلي
- احفظ كلمة المرور في مكان آمن
- يمكنك تغيير اسم قاعدة البيانات من `najah-academy` إلى أي اسم تريده
- الحساب المجاني يدعم حتى 512 MB من البيانات

## استكشاف الأخطاء:
- إذا فشل الاتصال، تأكد من إضافة عنوان IP الصحيح
- تأكد من صحة اسم المستخدم وكلمة المرور
- تأكد من أن الـ cluster في حالة "Running"
