#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer);
        });
    });
}

async function main() {
    console.log('🚀 مرحباً بك في أداة النشر الشاملة لأكاديمية النجاح\n');
    
    // اختيار منصة النشر
    console.log('📋 اختر منصة النشر:');
    console.log('1. Railway (مجاني مع قاعدة بيانات)');
    console.log('2. Vercel (مجاني للواجهات الأمامية)');
    console.log('3. إعداد قاعدة البيانات فقط');
    console.log('4. اختبار الاتصال بقاعدة البيانات');
    
    const choice = await askQuestion('\nاختر رقم الخيار (1-4): ');
    
    switch (choice.trim()) {
        case '1':
            await deployToRailway();
            break;
        case '2':
            await deployToVercel();
            break;
        case '3':
            await setupDatabase();
            break;
        case '4':
            await testDatabaseConnection();
            break;
        default:
            console.log('❌ خيار غير صحيح');
            process.exit(1);
    }
    
    rl.close();
}

async function deployToRailway() {
    console.log('\n🚂 نشر على Railway...');
    
    try {
        // التحقق من Railway CLI
        try {
            execSync('railway --version', { stdio: 'pipe' });
            console.log('✅ Railway CLI مثبت');
        } catch {
            console.log('📥 تثبيت Railway CLI...');
            execSync('npm install -g @railway/cli', { stdio: 'inherit' });
        }
        
        // تسجيل الدخول
        console.log('🔐 تسجيل الدخول إلى Railway...');
        execSync('railway login', { stdio: 'inherit' });
        
        // إنشاء أو ربط مشروع
        try {
            execSync('railway status', { stdio: 'pipe' });
            console.log('✅ مشروع Railway موجود');
        } catch {
            console.log('🆕 إنشاء مشروع جديد...');
            execSync('railway init', { stdio: 'inherit' });
        }
        
        // رفع متغيرات البيئة
        await uploadEnvironmentVariables('railway');
        
        // النشر
        console.log('🚀 نشر التطبيق...');
        execSync('railway up', { stdio: 'inherit' });
        
        console.log('\n✅ تم النشر بنجاح على Railway!');
        
        // الحصول على الرابط
        try {
            const domain = execSync('railway domain', { encoding: 'utf8' }).trim();
            console.log(`🌐 رابط التطبيق: ${domain}`);
        } catch {
            console.log('🌐 يمكنك الحصول على الرابط من لوحة تحكم Railway');
        }
        
    } catch (error) {
        console.error('❌ فشل في النشر على Railway:', error.message);
    }
}

async function deployToVercel() {
    console.log('\n⚡ نشر على Vercel...');
    
    try {
        // التحقق من Vercel CLI
        try {
            execSync('vercel --version', { stdio: 'pipe' });
            console.log('✅ Vercel CLI مثبت');
        } catch {
            console.log('📥 تثبيت Vercel CLI...');
            execSync('npm install -g vercel', { stdio: 'inherit' });
        }
        
        // تسجيل الدخول
        console.log('🔐 تسجيل الدخول إلى Vercel...');
        execSync('vercel login', { stdio: 'inherit' });
        
        // إعداد متغيرات البيئة
        console.log('\n⚠️ مهم: يجب إضافة متغيرات البيئة في لوحة تحكم Vercel');
        console.log('اذهب إلى: https://vercel.com/dashboard -> Project Settings -> Environment Variables');
        
        if (fs.existsSync('.env')) {
            console.log('\n📋 متغيرات البيئة المطلوبة:');
            const envContent = fs.readFileSync('.env', 'utf8');
            const envLines = envContent.split('\n').filter(line => 
                line.trim() && !line.startsWith('#')
            );
            
            envLines.forEach(line => {
                const [key, ...valueParts] = line.split('=');
                const value = valueParts.join('=');
                if (key && value) {
                    console.log(`${key}=${value}`);
                }
            });
        }
        
        const proceed = await askQuestion('\nهل أضفت المتغيرات؟ (y/n): ');
        if (proceed.toLowerCase() !== 'y') {
            console.log('يرجى إضافة المتغيرات أولاً');
            return;
        }
        
        // النشر
        console.log('🚀 نشر التطبيق...');
        execSync('vercel --prod', { stdio: 'inherit' });
        
        console.log('\n✅ تم النشر بنجاح على Vercel!');
        
    } catch (error) {
        console.error('❌ فشل في النشر على Vercel:', error.message);
    }
}

async function setupDatabase() {
    console.log('\n🗄️ إعداد قاعدة البيانات...');
    
    try {
        execSync('node setup-database.js', { stdio: 'inherit' });
    } catch (error) {
        console.error('❌ فشل في إعداد قاعدة البيانات:', error.message);
    }
}

async function testDatabaseConnection() {
    console.log('\n🔍 اختبار الاتصال بقاعدة البيانات...');
    
    try {
        const testScript = `
            require('dotenv').config();
            const mongoose = require('mongoose');
            
            mongoose.connect(process.env.MONGODB_URI)
                .then(() => {
                    console.log('✅ الاتصال بقاعدة البيانات ناجح');
                    console.log('📊 اسم قاعدة البيانات:', mongoose.connection.name);
                    process.exit(0);
                })
                .catch((error) => {
                    console.error('❌ فشل الاتصال:', error.message);
                    process.exit(1);
                });
        `;
        
        fs.writeFileSync('temp-test.js', testScript);
        execSync('node temp-test.js', { stdio: 'inherit' });
        fs.unlinkSync('temp-test.js');
        
    } catch (error) {
        console.error('❌ فشل في اختبار الاتصال:', error.message);
    }
}

async function uploadEnvironmentVariables(platform) {
    if (!fs.existsSync('.env')) {
        console.log('⚠️ ملف .env غير موجود');
        return;
    }
    
    console.log('🔧 رفع متغيرات البيئة...');
    const envContent = fs.readFileSync('.env', 'utf8');
    const envLines = envContent.split('\n').filter(line => 
        line.trim() && !line.startsWith('#')
    );
    
    for (const line of envLines) {
        const [key, ...valueParts] = line.split('=');
        const value = valueParts.join('=');
        if (key && value) {
            try {
                if (platform === 'railway') {
                    execSync(`railway variables set ${key}="${value}"`, { stdio: 'pipe' });
                }
                console.log(`✅ ${key} تم رفعه`);
            } catch (error) {
                console.log(`⚠️ تحذير: فشل في رفع ${key}`);
            }
        }
    }
}

main().catch(console.error);
