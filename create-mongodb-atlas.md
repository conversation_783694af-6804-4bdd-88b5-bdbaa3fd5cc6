# 🚀 إنشاء قاعدة بيانات MongoDB Atlas - دليل سريع

## الخطوات السريعة:

### 1. إنشاء حساب MongoDB Atlas
1. اذهب إلى: https://www.mongodb.com/cloud/atlas/register
2. أنشئ حساب جديد باستخدام Google أو GitHub
3. اختر "Build a Database"

### 2. إعداد Cluster مجاني
1. اختر **M0 Sandbox** (مجاني)
2. اختر **AWS** كمزود
3. اختر منطقة قريبة (مثل Frankfurt eu-central-1)
4. اسم الـ Cluster: `Cluster0`
5. انقر "Create Cluster"

### 3. إعداد Database User
1. اذهب إلى "Database Access"
2. انقر "Add New Database User"
3. اختر "Password" authentication
4. Username: `najahuser`
5. Password: `NajahAcademy2024!`
6. Database User Privileges: "Read and write to any database"
7. انقر "Add User"

### 4. إعداد Network Access
1. اذهب إلى "Network Access"
2. انقر "Add IP Address"
3. انقر "Allow Access from Anywhere" (0.0.0.0/0)
4. انقر "Confirm"

### 5. الحصول على Connection String
1. اذهب إلى "Database" -> "Connect"
2. اختر "Connect your application"
3. اختر "Node.js" و "4.1 or later"
4. انسخ Connection String

### 6. تحديث ملف .env
استبدل MONGODB_URI في ملف .env بـ:
```
MONGODB_URI=mongodb+srv://najahuser:NajahAcademy2024!@cluster0.xxxxx.mongodb.net/najah-academy?retryWrites=true&w=majority
```

**مهم:** استبدل `xxxxx` بالرقم الفعلي من Connection String الخاص بك

### 7. اختبار الاتصال
```bash
node setup-database.js
```

## نصائح:
- احفظ كلمة المرور في مكان آمن
- يمكنك تغيير اسم قاعدة البيانات من `najah-academy` إلى أي اسم
- الحساب المجاني يدعم حتى 512 MB
- إذا فشل الاتصال، تأكد من إضافة IP Address الصحيح

## Connection String مثال:
```
mongodb+srv://najahuser:NajahAcademy2024!@cluster0.abc123.mongodb.net/najah-academy?retryWrites=true&w=majority
```
